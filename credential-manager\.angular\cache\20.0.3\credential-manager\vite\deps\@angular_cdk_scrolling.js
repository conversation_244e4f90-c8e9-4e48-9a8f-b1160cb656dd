import {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory
} from "./chunk-4QP4CWJT.js";
import "./chunk-EOFW2REK.js";
import "./chunk-WT4TBBTP.js";
import {
  Dir
} from "./chunk-T2QHUC3W.js";
import "./chunk-M4U7ZGXH.js";
import "./chunk-5WZ7CC4M.js";
import "./chunk-RL4FXNAU.js";
import "./chunk-TYLNY5KT.js";
import "./chunk-FQBI3WRC.js";
import "./chunk-4DJR3ZNH.js";
import "./chunk-OCBFZOLU.js";
export {
  CdkFixedSizeVirtualScroll,
  CdkScrollable,
  CdkScrollableModule,
  CdkVirtualForOf,
  CdkVirtualScrollViewport,
  CdkVirtualScrollable,
  CdkVirtualScrollableElement,
  CdkVirtualScrollableWindow,
  DEFAULT_RESIZE_TIME,
  DEFAULT_SCROLL_TIME,
  FixedSizeVirtualScrollStrategy,
  ScrollDispatcher,
  ScrollingModule,
  VIRTUAL_SCROLLABLE,
  VIRTUAL_SCROLL_STRATEGY,
  ViewportRuler,
  _fixedSizeVirtualScrollStrategyFactory,
  Dir as ɵɵDir
};
